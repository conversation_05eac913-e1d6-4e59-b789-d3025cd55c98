//@version=5
indicator("Smart Money Concepts (SMC) Indicator", shorttitle="SMC", overlay=true, max_lines_count=500, max_boxes_count=500)

// ═══════════════════════════════════════════════════════════════════════════════════════
// SMART MONEY CONCEPTS (SMC) INDICATOR v1.0
// ═══════════════════════════════════════════════════════════════════════════════════════
// Author: AI Assistant (Augment Agent)
// Version: 1.0.0
// Pine Script Version: v5
// Created: 2025-06-20
// 
// Description: Comprehensive Smart Money Concepts indicator featuring automated market
// structure identification, Break of Structure (BOS) detection, Fair Value Gaps (FVG),
// Order Blocks (OB), and liquidity analysis.
//
// Features:
// - Swing High/Low Detection with configurable sensitivity
// - Market Structure Visualization (Support/Resistance levels)
// - Break of Structure (BOS) Signal Generation
// - Fair Value Gaps (FVG) Identification and Tracking
// - Order Blocks (OB) Detection and Management
// - Liquidity Zone Analysis
// - Multi-timeframe Compatibility
// - Real-time Alerts and Notifications
// ═══════════════════════════════════════════════════════════════════════════════════════

// ═══════════════════════════════════════════════════════════════════════════════════════
// INPUT SETTINGS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Structure Detection Settings
structureGroup = "Structure Detection"
swingLength = input.int(5, title="Swing Detection Length", minval=1, maxval=20, group=structureGroup, tooltip="Number of bars to look back for swing high/low detection")
structureConfirmation = input.int(1, title="Structure Confirmation Bars", minval=0, maxval=5, group=structureGroup, tooltip="Additional bars required to confirm swing points")
useBodyForSwings = input.bool(true, title="Use Candle Body for Swings", group=structureGroup, tooltip="Use close price instead of high/low for swing detection")

// Break of Structure Settings
bosGroup = "Break of Structure (BOS)"
enableBOS = input.bool(true, title="Enable BOS Detection", group=bosGroup)
bosConfirmation = input.bool(true, title="Require Full Body Closure", group=bosGroup, tooltip="Require full candle body to close beyond structure level")
bosVolumeFilter = input.bool(false, title="Volume Confirmation Filter", group=bosGroup, tooltip="Require above-average volume for BOS confirmation")
volumeLookback = input.int(20, title="Volume Average Lookback", minval=5, maxval=100, group=bosGroup, tooltip="Periods for volume average calculation")

// Visual Settings
visualGroup = "Visual Settings"
showStructureLines = input.bool(true, title="Show Structure Lines", group=visualGroup)
showTradingRange = input.bool(true, title="Show Trading Range Box", group=visualGroup)
structureLineStyle = input.string("Solid", title="Structure Line Style", options=["Solid", "Dashed", "Dotted"], group=visualGroup)
structureLineWidth = input.int(2, title="Structure Line Width", minval=1, maxval=5, group=visualGroup)

// Color Settings
colorGroup = "Color Settings"
bullishStructureColor = input.color(color.new(color.green, 0), title="Bullish Structure Color", group=colorGroup)
bearishStructureColor = input.color(color.new(color.red, 0), title="Bearish Structure Color", group=colorGroup)
rangeBoxColor = input.color(color.new(color.blue, 80), title="Trading Range Box Color", group=colorGroup)
bosSignalColorBull = input.color(color.new(color.lime, 0), title="Bullish BOS Signal Color", group=colorGroup)
bosSignalColorBear = input.color(color.new(color.fuchsia, 0), title="Bearish BOS Signal Color", group=colorGroup)

// Alert Settings
alertGroup = "Alert Settings"
enableAlerts = input.bool(true, title="Enable Alerts", group=alertGroup)
alertOnBOS = input.bool(true, title="Alert on Break of Structure", group=alertGroup)
alertOnStructureChange = input.bool(false, title="Alert on Structure Change", group=alertGroup)

// Fair Value Gaps Settings
fvgGroup = "Fair Value Gaps (FVG)"
enableFVG = input.bool(true, title="Enable FVG Detection", group=fvgGroup)
showBullishFVG = input.bool(true, title="Show Bullish FVG", group=fvgGroup)
showBearishFVG = input.bool(true, title="Show Bearish FVG", group=fvgGroup)
fvgTransparency = input.int(75, title="FVG Box Transparency", minval=0, maxval=95, group=fvgGroup)
autoRemoveFVG = input.bool(true, title="Auto Remove Filled FVG", group=fvgGroup, tooltip="Automatically remove FVG when price fills the gap")
maxFVGBoxes = input.int(20, title="Max FVG Boxes", minval=5, maxval=50, group=fvgGroup)
bullishFVGColor = input.color(color.new(color.green, 75), title="Bullish FVG Color", group=fvgGroup)
bearishFVGColor = input.color(color.new(color.red, 75), title="Bearish FVG Color", group=fvgGroup)

// Order Block Settings
obGroup = "Order Blocks (OB)"
enableOB = input.bool(true, title="Enable Order Block Detection", group=obGroup)
showBullishOB = input.bool(true, title="Show Bullish Order Blocks", group=obGroup)
showBearishOB = input.bool(true, title="Show Bearish Order Blocks", group=obGroup)
obTransparency = input.int(80, title="OB Box Transparency", minval=0, maxval=95, group=obGroup)
obVolumeFilter = input.bool(true, title="Volume Filter for OB", group=obGroup, tooltip="Require above-average volume for order block confirmation")
maxOBBoxes = input.int(15, title="Max Order Block Boxes", minval=5, maxval=30, group=obGroup)
obExpireBars = input.int(50, title="OB Expiration Bars", minval=10, maxval=200, group=obGroup, tooltip="Remove untested order blocks after this many bars")
bullishOBColor = input.color(color.new(color.blue, 80), title="Bullish OB Color", group=obGroup)
bearishOBColor = input.color(color.new(color.orange, 80), title="Bearish OB Color", group=obGroup)

// Liquidity Settings
liquidityGroup = "Liquidity Analysis"
enableLiquidity = input.bool(true, title="Enable Liquidity Detection", group=liquidityGroup)
showEqualHighsLows = input.bool(true, title="Show Equal Highs/Lows", group=liquidityGroup)
equalLevelTolerance = input.float(0.1, title="Equal Level Tolerance %", minval=0.01, maxval=1.0, group=liquidityGroup, tooltip="Percentage tolerance for equal highs/lows detection")
liquidityLookback = input.int(10, title="Liquidity Lookback Period", minval=5, maxval=50, group=liquidityGroup)
liquidityColor = input.color(color.new(color.yellow, 60), title="Liquidity Zone Color", group=liquidityGroup)

// Advanced Settings
advancedGroup = "Advanced Settings"
maxHistoricalStructures = input.int(10, title="Max Historical Structures", minval=1, maxval=50, group=advancedGroup, tooltip="Maximum number of historical structure levels to maintain")
antiGapProtection = input.bool(true, title="Anti-Gap Protection", group=advancedGroup, tooltip="Prevent false signals on market gaps")

// ═══════════════════════════════════════════════════════════════════════════════════════
// VARIABLE DECLARATIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Structure tracking variables
var float lastSwingHigh = na
var float lastSwingLow = na
var int lastSwingHighBar = na
var int lastSwingLowBar = na
var bool isUptrend = na
var bool isDowntrend = na

// Line and box management arrays
var array<line> structureLines = array.new<line>()
var array<box> rangeBboxes = array.new<box>()
var array<label> bosLabels = array.new<label>()

// FVG tracking arrays
var array<box> fvgBoxes = array.new<box>()
var array<float> fvgTops = array.new<float>()
var array<float> fvgBottoms = array.new<float>()
var array<bool> fvgIsBullish = array.new<bool>()

// Order Block and Liquidity tracking arrays
var array<box> obBoxes = array.new<box>()
var array<float> obTops = array.new<float>()
var array<float> obBottoms = array.new<float>()
var array<bool> obIsBullish = array.new<bool>()
var array<int> obCreationBars = array.new<int>()
var array<line> liquidityLines = array.new<line>()

// BOS tracking variables
var float lastBOSLevel = na
var bool lastBOSWasBullish = na
var int lastBOSBar = na

// Volume analysis
volumeAverage = ta.sma(volume, volumeLookback)
isHighVolume = volume > volumeAverage * 1.2

// ═══════════════════════════════════════════════════════════════════════════════════════
// UTILITY FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Function to get line style based on input
getLineStyle() =>
    switch structureLineStyle
        "Solid" => line.style_solid
        "Dashed" => line.style_dashed
        "Dotted" => line.style_dotted
        => line.style_solid

// Function to clean up old drawing objects
cleanupOldObjects() =>
    // Remove excess structure lines
    while array.size(structureLines) > maxHistoricalStructures
        oldLine = array.shift(structureLines)
        line.delete(oldLine)
    
    // Remove excess range boxes
    while array.size(rangeBboxes) > 5
        oldBox = array.shift(rangeBboxes)
        box.delete(oldBox)
    
    // Remove excess BOS labels
    while array.size(bosLabels) > maxHistoricalStructures
        oldLabel = array.shift(bosLabels)
        label.delete(oldLabel)

// Function to check if current bar has gap
hasGap() =>
    if antiGapProtection and bar_index > 0
        prevHigh = high[1]
        prevLow = low[1]
        currentHigh = high
        currentLow = low
        
        // Check for gap up or gap down
        gapUp = currentLow > prevHigh
        gapDown = currentHigh < prevLow
        
        gapUp or gapDown
    else
        false

// ═══════════════════════════════════════════════════════════════════════════════════════
// SWING POINT DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Function to detect swing highs
isSwingHigh(length) =>
    if bar_index < length
        false
    else
        highPrice = useBodyForSwings ? math.max(close, open) : high
        isHigh = true
        
        // Check left side
        for i = 1 to length
            if highPrice <= (useBodyForSwings ? math.max(close[i], open[i]) : high[i])
                isHigh := false
                break
        
        // Check right side (if we have enough bars)
        if isHigh and bar_index >= length + structureConfirmation
            for i = 1 to length
                if highPrice <= (useBodyForSwings ? math.max(close[i], open[i]) : high[i])
                    isHigh := false
                    break
        
        isHigh

// Function to detect swing lows
isSwingLow(length) =>
    if bar_index < length
        false
    else
        lowPrice = useBodyForSwings ? math.min(close, open) : low
        isLow = true
        
        // Check left side
        for i = 1 to length
            if lowPrice >= (useBodyForSwings ? math.min(close[i], open[i]) : low[i])
                isLow := false
                break
        
        // Check right side (if we have enough bars)
        if isLow and bar_index >= length + structureConfirmation
            for i = 1 to length
                if lowPrice >= (useBodyForSwings ? math.min(close[i], open[i]) : low[i])
                    isLow := false
                    break
        
        isLow

// Detect current swing points
currentSwingHigh = isSwingHigh(swingLength)
currentSwingLow = isSwingLow(swingLength)

// ═══════════════════════════════════════════════════════════════════════════════════════
// MARKET STRUCTURE ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Update swing high tracking
if currentSwingHigh and not hasGap()
    newSwingHigh = useBodyForSwings ? math.max(close, open) : high
    lastSwingHigh := newSwingHigh
    lastSwingHighBar := bar_index

// Update swing low tracking
if currentSwingLow and not hasGap()
    newSwingLow = useBodyForSwings ? math.min(close, open) : low
    lastSwingLow := newSwingLow
    lastSwingLowBar := bar_index

// Determine market structure trend
if not na(lastSwingHigh) and not na(lastSwingLow)
    if lastSwingHighBar > lastSwingLowBar
        isUptrend := true
        isDowntrend := false
    else
        isUptrend := false
        isDowntrend := true

// ═══════════════════════════════════════════════════════════════════════════════════════
// VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Draw structure lines
if showStructureLines and not na(lastSwingHigh) and not na(lastSwingLow)
    // Draw swing high line
    if not na(lastSwingHighBar)
        highLine = line.new(
            x1=lastSwingHighBar, y1=lastSwingHigh,
            x2=bar_index + 10, y2=lastSwingHigh,
            color=bullishStructureColor,
            style=getLineStyle(),
            width=structureLineWidth,
            extend=extend.right
        )
        array.push(structureLines, highLine)
    
    // Draw swing low line
    if not na(lastSwingLowBar)
        lowLine = line.new(
            x1=lastSwingLowBar, y1=lastSwingLow,
            x2=bar_index + 10, y2=lastSwingLow,
            color=bearishStructureColor,
            style=getLineStyle(),
            width=structureLineWidth,
            extend=extend.right
        )
        array.push(structureLines, lowLine)

// Draw trading range box
if showTradingRange and not na(lastSwingHigh) and not na(lastSwingLow)
    rangeBox = box.new(
        left=math.min(lastSwingHighBar, lastSwingLowBar),
        top=lastSwingHigh,
        right=bar_index + 20,
        bottom=lastSwingLow,
        bgcolor=rangeBoxColor,
        border_color=color.new(color.blue, 50),
        border_width=1,
        extend=extend.right
    )
    array.push(rangeBboxes, rangeBox)

// Clean up old objects
if bar_index % 10 == 0  // Clean up every 10 bars for performance
    cleanupOldObjects()

// ═══════════════════════════════════════════════════════════════════════════════════════
// PLOTTING AND ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot swing points for debugging (optional)
plotshape(currentSwingHigh, title="Swing High", style=shape.triangledown, location=location.abovebar, color=color.red, size=size.tiny)
plotshape(currentSwingLow, title="Swing Low", style=shape.triangleup, location=location.belowbar, color=color.green, size=size.tiny)

// Basic structure change alerts
if enableAlerts and alertOnStructureChange
    if currentSwingHigh
        alert("New Swing High detected at " + str.tostring(lastSwingHigh), alert.freq_once_per_bar)
    if currentSwingLow
        alert("New Swing Low detected at " + str.tostring(lastSwingLow), alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════════════
// PHASE 2: BREAK OF STRUCTURE (BOS) DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Function to check if price has broken structure level
checkBOSCondition(structureLevel, isBullishBOS) =>
    if na(structureLevel)
        false
    else
        if isBullishBOS
            // Bullish BOS: Close above previous swing high
            if bosConfirmation
                close > structureLevel and (not bosVolumeFilter or isHighVolume)
            else
                high > structureLevel and (not bosVolumeFilter or isHighVolume)
        else
            // Bearish BOS: Close below previous swing low
            if bosConfirmation
                close < structureLevel and (not bosVolumeFilter or isHighVolume)
            else
                low < structureLevel and (not bosVolumeFilter or isHighVolume)

// BOS Detection Logic
var bool bosDetected = false
var bool isBullishBOS = false
var float bosLevel = na
var string bosMessage = ""

if enableBOS and not hasGap()
    // Check for Bullish BOS (break above swing high)
    if not na(lastSwingHigh) and checkBOSCondition(lastSwingHigh, true)
        if lastBOSLevel != lastSwingHigh or not lastBOSWasBullish
            bosDetected := true
            isBullishBOS := true
            bosLevel := lastSwingHigh
            bosMessage := "Bullish BOS at " + str.tostring(lastSwingHigh, "#.####")
            lastBOSLevel := lastSwingHigh
            lastBOSWasBullish := true
            lastBOSBar := bar_index

    // Check for Bearish BOS (break below swing low)
    else if not na(lastSwingLow) and checkBOSCondition(lastSwingLow, false)
        if lastBOSLevel != lastSwingLow or lastBOSWasBullish
            bosDetected := true
            isBullishBOS := false
            bosLevel := lastSwingLow
            bosMessage := "Bearish BOS at " + str.tostring(lastSwingLow, "#.####")
            lastBOSLevel := lastSwingLow
            lastBOSWasBullish := false
            lastBOSBar := bar_index
    else
        bosDetected := false

// ═══════════════════════════════════════════════════════════════════════════════════════
// BOS SIGNAL VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Draw BOS signals
if bosDetected and enableBOS
    if isBullishBOS
        // Bullish BOS signal
        bosShape = shape.new(
            point.new(bar_index, low),
            shape.triangleup,
            size=size.normal,
            color=bosSignalColorBull,
            text="BOS↑",
            text_color=color.white,
            text_size=size.small
        )

        bosLabel = label.new(
            x=bar_index,
            y=low,
            text="Bullish BOS\n" + str.tostring(bosLevel, "#.####"),
            style=label.style_label_up,
            color=bosSignalColorBull,
            textcolor=color.white,
            size=size.normal
        )
        array.push(bosLabels, bosLabel)
    else
        // Bearish BOS signal
        bosShape = shape.new(
            point.new(bar_index, high),
            shape.triangledown,
            size=size.normal,
            color=bosSignalColorBear,
            text="BOS↓",
            text_color=color.white,
            text_size=size.small
        )

        bosLabel = label.new(
            x=bar_index,
            y=high,
            text="Bearish BOS\n" + str.tostring(bosLevel, "#.####"),
            style=label.style_label_down,
            color=bosSignalColorBear,
            textcolor=color.white,
            size=size.normal
        )
        array.push(bosLabels, bosLabel)

// ═══════════════════════════════════════════════════════════════════════════════════════
// BOS ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// BOS Alert System
if enableAlerts and alertOnBOS and bosDetected
    alert(bosMessage, alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════════════
// ENHANCED PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot BOS levels for reference
plot(enableBOS and not na(lastBOSLevel) ? lastBOSLevel : na,
     title="Last BOS Level",
     color=lastBOSWasBullish ? color.new(color.green, 70) : color.new(color.red, 70),
     linewidth=1,
     style=plot.style_circles)

// ═══════════════════════════════════════════════════════════════════════════════════════
// PHASE 3: FAIR VALUE GAPS (FVG) DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════





// Function to detect Fair Value Gaps
detectFVG() =>
    var bool bullishFVG = false
    var bool bearishFVG = false
    var float gapTop = na
    var float gapBottom = na

    if bar_index >= 2
        // Current candle (candle 0)
        currentHigh = high
        currentLow = low

        // Previous candle (candle 1)
        prevHigh = high[1]
        prevLow = low[1]

        // Two candles ago (candle 2)
        prev2High = high[2]
        prev2Low = low[2]

        // Bullish FVG: Gap between candle[2] high and candle[0] low
        // with candle[1] creating the gap
        if prevLow > prev2High and currentLow < prevHigh
            bullishFVG := true
            gapTop := prevLow
            gapBottom := prev2High

        // Bearish FVG: Gap between candle[2] low and candle[0] high
        // with candle[1] creating the gap
        else if prevHigh < prev2Low and currentHigh > prevLow
            bearishFVG := true
            gapTop := prev2Low
            gapBottom := prevHigh

    [bullishFVG, bearishFVG, gapTop, gapBottom]

// Function to check if FVG is filled
isFVGFilled(gapTop, gapBottom, isBullish) =>
    if isBullish
        // Bullish FVG is filled when price goes back down into the gap
        low <= gapBottom
    else
        // Bearish FVG is filled when price goes back up into the gap
        high >= gapTop

// Function to clean up filled FVGs
cleanupFilledFVGs() =>
    if autoRemoveFVG and array.size(fvgBoxes) > 0
        i = 0
        while i < array.size(fvgBoxes)
            if i < array.size(fvgTops) and i < array.size(fvgBottoms) and i < array.size(fvgIsBullish)
                gapTop = array.get(fvgTops, i)
                gapBottom = array.get(fvgBottoms, i)
                isBullish = array.get(fvgIsBullish, i)

                if isFVGFilled(gapTop, gapBottom, isBullish)
                    // Remove filled FVG
                    fvgBox = array.get(fvgBoxes, i)
                    box.delete(fvgBox)
                    array.remove(fvgBoxes, i)
                    array.remove(fvgTops, i)
                    array.remove(fvgBottoms, i)
                    array.remove(fvgIsBullish, i)
                else
                    i += 1
            else
                i += 1

// Detect FVGs
[bullishFVGDetected, bearishFVGDetected, gapTop, gapBottom] = detectFVG()

// Create FVG boxes
if enableFVG and not hasGap()
    if bullishFVGDetected and showBullishFVG
        fvgBox = box.new(
            left=bar_index - 1,
            top=gapTop,
            right=bar_index + 20,
            bottom=gapBottom,
            bgcolor=bullishFVGColor,
            border_color=color.new(color.green, 50),
            border_width=1,
            extend=extend.right,
            text="Bullish FVG",
            text_color=color.green,
            text_size=size.small
        )

        array.push(fvgBoxes, fvgBox)
        array.push(fvgTops, gapTop)
        array.push(fvgBottoms, gapBottom)
        array.push(fvgIsBullish, true)

    if bearishFVGDetected and showBearishFVG
        fvgBox = box.new(
            left=bar_index - 1,
            top=gapTop,
            right=bar_index + 20,
            bottom=gapBottom,
            bgcolor=bearishFVGColor,
            border_color=color.new(color.red, 50),
            border_width=1,
            extend=extend.right,
            text="Bearish FVG",
            text_color=color.red,
            text_size=size.small
        )

        array.push(fvgBoxes, fvgBox)
        array.push(fvgTops, gapTop)
        array.push(fvgBottoms, gapBottom)
        array.push(fvgIsBullish, false)

// Clean up filled FVGs and manage box count
if bar_index % 5 == 0  // Check every 5 bars for performance
    cleanupFilledFVGs()

    // Remove excess FVG boxes
    while array.size(fvgBoxes) > maxFVGBoxes
        oldBox = array.shift(fvgBoxes)
        box.delete(oldBox)
        array.shift(fvgTops)
        array.shift(fvgBottoms)
        array.shift(fvgIsBullish)

// ═══════════════════════════════════════════════════════════════════════════════════════
// PHASE 4: ORDER BLOCKS (OB) AND LIQUIDITY ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════



// Function to detect Order Blocks
detectOrderBlock() =>
    var bool bullishOB = false
    var bool bearishOB = false
    var float obTop = na
    var float obBottom = na

    if bar_index >= 3
        // Look for the last bullish candle before a bearish move (Bullish OB)
        if close[2] > open[2] and close[1] < open[1] and close < open  // Bullish candle followed by bearish moves
            if not obVolumeFilter or volume[2] > volumeAverage
                bullishOB := true
                obTop := high[2]
                obBottom := low[2]

        // Look for the last bearish candle before a bullish move (Bearish OB)
        else if close[2] < open[2] and close[1] > open[1] and close > open  // Bearish candle followed by bullish moves
            if not obVolumeFilter or volume[2] > volumeAverage
                bearishOB := true
                obTop := high[2]
                obBottom := low[2]

    [bullishOB, bearishOB, obTop, obBottom]

// Function to check if Order Block is tested
isOBTested(obTop, obBottom, isBullish) =>
    if isBullish
        // Bullish OB is tested when price comes back down into the block
        low <= obBottom or close <= obBottom
    else
        // Bearish OB is tested when price comes back up into the block
        high >= obTop or close >= obTop

// Function to detect Equal Highs/Lows (Liquidity)
detectLiquidity() =>
    var array<float> recentHighs = array.new<float>()
    var array<float> recentLows = array.new<float>()
    var bool equalHighsFound = false
    var bool equalLowsFound = false
    var float liquidityLevel = na

    // Collect recent swing highs and lows
    if currentSwingHigh
        array.push(recentHighs, high)
        if array.size(recentHighs) > liquidityLookback
            array.shift(recentHighs)

    if currentSwingLow
        array.push(recentLows, low)
        if array.size(recentLows) > liquidityLookback
            array.shift(recentLows)

    // Check for equal highs
    if array.size(recentHighs) >= 2
        lastHigh = array.get(recentHighs, array.size(recentHighs) - 1)
        for i = 0 to array.size(recentHighs) - 2
            compareHigh = array.get(recentHighs, i)
            tolerance = lastHigh * equalLevelTolerance / 100
            if math.abs(lastHigh - compareHigh) <= tolerance
                equalHighsFound := true
                liquidityLevel := lastHigh
                break

    // Check for equal lows
    if array.size(recentLows) >= 2 and not equalHighsFound
        lastLow = array.get(recentLows, array.size(recentLows) - 1)
        for i = 0 to array.size(recentLows) - 2
            compareLow = array.get(recentLows, i)
            tolerance = lastLow * equalLevelTolerance / 100
            if math.abs(lastLow - compareLow) <= tolerance
                equalLowsFound := true
                liquidityLevel := lastLow
                break

    [equalHighsFound, equalLowsFound, liquidityLevel]

// Detect Order Blocks
[bullishOBDetected, bearishOBDetected, obTop, obBottom] = detectOrderBlock()

// Create Order Block boxes
if enableOB and not hasGap()
    if bullishOBDetected and showBullishOB
        obBox = box.new(
            left=bar_index - 2,
            top=obTop,
            right=bar_index + 30,
            bottom=obBottom,
            bgcolor=bullishOBColor,
            border_color=color.new(color.blue, 50),
            border_width=2,
            extend=extend.right,
            text="Bullish OB",
            text_color=color.blue,
            text_size=size.small
        )

        array.push(obBoxes, obBox)
        array.push(obTops, obTop)
        array.push(obBottoms, obBottom)
        array.push(obIsBullish, true)
        array.push(obCreationBars, bar_index)

    if bearishOBDetected and showBearishOB
        obBox = box.new(
            left=bar_index - 2,
            top=obTop,
            right=bar_index + 30,
            bottom=obBottom,
            bgcolor=bearishOBColor,
            border_color=color.new(color.orange, 50),
            border_width=2,
            extend=extend.right,
            text="Bearish OB",
            text_color=color.orange,
            text_size=size.small
        )

        array.push(obBoxes, obBox)
        array.push(obTops, obTop)
        array.push(obBottoms, obBottom)
        array.push(obIsBullish, false)
        array.push(obCreationBars, bar_index)

// Detect and visualize liquidity
[equalHighsFound, equalLowsFound, liquidityLevel] = detectLiquidity()

if enableLiquidity and showEqualHighsLows and not na(liquidityLevel)
    if equalHighsFound or equalLowsFound
        liquidityLine = line.new(
            x1=bar_index - liquidityLookback,
            y1=liquidityLevel,
            x2=bar_index + 10,
            y2=liquidityLevel,
            color=liquidityColor,
            style=line.style_dashed,
            width=2,
            extend=extend.right
        )
        array.push(liquidityLines, liquidityLine)

        // Add liquidity label
        liquidityLabel = label.new(
            x=bar_index,
            y=liquidityLevel,
            text=equalHighsFound ? "EQH" : "EQL",
            style=label.style_label_left,
            color=liquidityColor,
            textcolor=color.black,
            size=size.small
        )

// Function to clean up expired and tested Order Blocks
cleanupOrderBlocks() =>
    if array.size(obBoxes) > 0
        i = 0
        while i < array.size(obBoxes)
            if i < array.size(obTops) and i < array.size(obBottoms) and i < array.size(obIsBullish) and i < array.size(obCreationBars)
                obTop = array.get(obTops, i)
                obBottom = array.get(obBottoms, i)
                isBullish = array.get(obIsBullish, i)
                creationBar = array.get(obCreationBars, i)

                // Check if OB is tested or expired
                if isOBTested(obTop, obBottom, isBullish) or (bar_index - creationBar) > obExpireBars
                    // Remove tested/expired OB
                    obBox = array.get(obBoxes, i)
                    box.delete(obBox)
                    array.remove(obBoxes, i)
                    array.remove(obTops, i)
                    array.remove(obBottoms, i)
                    array.remove(obIsBullish, i)
                    array.remove(obCreationBars, i)
                else
                    i += 1
            else
                i += 1

// Function to clean up excess liquidity lines
cleanupLiquidityLines() =>
    while array.size(liquidityLines) > 20
        oldLine = array.shift(liquidityLines)
        line.delete(oldLine)

// Enhanced cleanup function
enhancedCleanup() =>
    cleanupOldObjects()
    cleanupFilledFVGs()
    cleanupOrderBlocks()
    cleanupLiquidityLines()

    // Remove excess OB boxes
    while array.size(obBoxes) > maxOBBoxes
        oldBox = array.shift(obBoxes)
        box.delete(oldBox)
        array.shift(obTops)
        array.shift(obBottoms)
        array.shift(obIsBullish)
        array.shift(obCreationBars)

// Run enhanced cleanup every 10 bars
if bar_index % 10 == 0
    enhancedCleanup()

// ═══════════════════════════════════════════════════════════════════════════════════════
// COMPREHENSIVE ALERT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════

// Enhanced alert messages
if enableAlerts
    if alertOnBOS and bosDetected
        alert(bosMessage, alert.freq_once_per_bar)

    if enableFVG and (bullishFVGDetected or bearishFVGDetected)
        fvgMessage = bullishFVGDetected ? "Bullish Fair Value Gap detected" : "Bearish Fair Value Gap detected"
        alert(fvgMessage, alert.freq_once_per_bar)

    if enableOB and (bullishOBDetected or bearishOBDetected)
        obMessage = bullishOBDetected ? "Bullish Order Block formed" : "Bearish Order Block formed"
        alert(obMessage, alert.freq_once_per_bar)

    if enableLiquidity and (equalHighsFound or equalLowsFound)
        liquidityMessage = equalHighsFound ? "Equal Highs (Liquidity) detected" : "Equal Lows (Liquidity) detected"
        alert(liquidityMessage, alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════════════
// FINAL PLOTTING AND TABLE DISPLAY
// ═══════════════════════════════════════════════════════════════════════════════════════

// Create information table
if barstate.islast
    var table infoTable = table.new(position.top_right, 2, 8, bgcolor=color.new(color.white, 80), border_width=1)

    table.cell(infoTable, 0, 0, "SMC Indicator", text_color=color.black, text_size=size.normal)
    table.cell(infoTable, 1, 0, "Status", text_color=color.black, text_size=size.normal)

    table.cell(infoTable, 0, 1, "Structure Detection", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 1, "✓ Active", text_color=color.green, text_size=size.small)

    table.cell(infoTable, 0, 2, "BOS Detection", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 2, enableBOS ? "✓ Active" : "✗ Disabled", text_color=enableBOS ? color.green : color.red, text_size=size.small)

    table.cell(infoTable, 0, 3, "Fair Value Gaps", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 3, enableFVG ? "✓ Active" : "✗ Disabled", text_color=enableFVG ? color.green : color.red, text_size=size.small)

    table.cell(infoTable, 0, 4, "Order Blocks", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 4, enableOB ? "✓ Active" : "✗ Disabled", text_color=enableOB ? color.green : color.red, text_size=size.small)

    table.cell(infoTable, 0, 5, "Liquidity Analysis", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 5, enableLiquidity ? "✓ Active" : "✗ Disabled", text_color=enableLiquidity ? color.green : color.red, text_size=size.small)

    table.cell(infoTable, 0, 6, "Active FVGs", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 6, str.tostring(array.size(fvgBoxes)), text_color=color.blue, text_size=size.small)

    table.cell(infoTable, 0, 7, "Active OBs", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 7, str.tostring(array.size(obBoxes)), text_color=color.blue, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════
// END OF COMPREHENSIVE SMC INDICATOR IMPLEMENTATION
// ═══════════════════════════════════════════════════════════════════════════════════════
// Version: 1.0.0 - Full Feature Set Complete
// All Phases Implemented: Structure Detection, BOS, FVG, Order Blocks, Liquidity Analysis
// ═══════════════════════════════════════════════════════════════════════════════════════
