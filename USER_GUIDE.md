# Smart Money Concepts (SMC) Indicator - User Guide

## Overview
The Smart Money Concepts (SMC) Indicator is a comprehensive TradingView Pine Script v5 tool designed to identify and visualize key market structure elements used by institutional traders. This indicator combines multiple SMC concepts into a single, powerful analysis tool.

## Features

### 🔍 Core Structure Detection
- **Swing High/Low Detection**: Automatically identifies market structure pivot points
- **Market Structure Visualization**: Dynamic support/resistance lines and trading ranges
- **Configurable Sensitivity**: Adjustable lookback periods (1-20 bars)
- **Anti-Gap Protection**: Prevents false signals during market gaps

### 📈 Break of Structure (BOS) Analysis
- **Bullish BOS**: Detects when price breaks above previous swing highs
- **Bearish BOS**: Identifies breaks below previous swing lows
- **Volume Confirmation**: Optional volume filter for signal validation
- **Real-time Alerts**: Instant notifications for structure breaks

### 🎯 Fair Value Gaps (FVG)
- **Gap Detection**: Identifies 3-candle imbalance patterns
- **Automatic Management**: Removes gaps when filled by price action
- **Visual Clarity**: Color-coded boxes for bullish (green) and bearish (red) gaps
- **Historical Tracking**: Maintains active gaps until filled

### 🏢 Order Blocks (OB)
- **Institutional Zones**: Identifies last opposing candle before structure breaks
- **Volume Filtering**: Requires significant volume for confirmation
- **Expiration Logic**: Removes untested blocks after specified time
- **Visual Distinction**: Blue boxes for bullish, orange for bearish order blocks

### 💧 Liquidity Analysis
- **Equal Highs/Lows**: Detects potential liquidity zones
- **Tolerance Settings**: Configurable percentage for level matching
- **Visual Markers**: Dashed lines highlighting liquidity areas
- **Stop Hunt Identification**: Marks areas where stops may be targeted

## Settings Guide

### Structure Detection Settings
- **Swing Detection Length** (1-20): Number of bars for swing point confirmation
- **Structure Confirmation Bars** (0-5): Additional bars required for validation
- **Use Candle Body for Swings**: Toggle between body vs. wick-based detection

### Break of Structure Settings
- **Enable BOS Detection**: Master toggle for BOS functionality
- **Require Full Body Closure**: Ensures complete candle closure beyond structure
- **Volume Confirmation Filter**: Requires above-average volume for BOS
- **Volume Average Lookback** (5-100): Periods for volume average calculation

### Visual Settings
- **Show Structure Lines**: Toggle support/resistance line display
- **Show Trading Range Box**: Display current trading range visualization
- **Structure Line Style**: Choose between Solid, Dashed, or Dotted lines
- **Structure Line Width** (1-5): Adjust line thickness

### Color Settings
- **Bullish Structure Color**: Color for upward structure elements
- **Bearish Structure Color**: Color for downward structure elements
- **Range Box Color**: Trading range background color
- **BOS Signal Colors**: Separate colors for bullish/bearish BOS signals

### Fair Value Gaps Settings
- **Enable FVG Detection**: Master toggle for FVG functionality
- **Show Bullish/Bearish FVG**: Individual toggles for gap types
- **FVG Box Transparency** (0-95): Adjust box opacity
- **Auto Remove Filled FVG**: Automatically delete filled gaps
- **Max FVG Boxes** (5-50): Limit number of active gaps

### Order Blocks Settings
- **Enable Order Block Detection**: Master toggle for OB functionality
- **Show Bullish/Bearish OB**: Individual toggles for block types
- **Volume Filter for OB**: Require above-average volume
- **OB Expiration Bars** (10-200): Remove untested blocks after X bars
- **Max Order Block Boxes** (5-30): Limit number of active blocks

### Liquidity Analysis Settings
- **Enable Liquidity Detection**: Master toggle for liquidity features
- **Show Equal Highs/Lows**: Display liquidity zone markers
- **Equal Level Tolerance %** (0.01-1.0): Percentage tolerance for matching levels
- **Liquidity Lookback Period** (5-50): Bars to analyze for equal levels

### Alert Settings
- **Enable Alerts**: Master toggle for all alert functionality
- **Alert on Break of Structure**: BOS-specific alerts
- **Alert on Structure Change**: Swing point change notifications

### Advanced Settings
- **Max Historical Structures** (1-50): Limit displayed structure elements
- **Anti-Gap Protection**: Prevent false signals on market gaps

## Usage Recommendations

### Timeframe Selection
- **Scalping (1m-5m)**: Use lower swing detection length (3-5)
- **Day Trading (15m-1h)**: Standard settings work well (5-10)
- **Swing Trading (4h-1D)**: Higher swing detection length (10-15)

### Market Type Considerations
- **Crypto Markets**: Enable anti-gap protection, use volume filters
- **Forex Markets**: Standard settings, focus on structure breaks
- **Stock Markets**: Enable volume confirmation for all features

### Best Practices
1. **Start Simple**: Enable core structure detection first, add features gradually
2. **Combine Signals**: Look for confluence between BOS, FVG, and OB signals
3. **Respect Higher Timeframes**: Use higher timeframe structure for context
4. **Volume Confirmation**: Enable volume filters in volatile markets
5. **Regular Cleanup**: Monitor active elements to avoid chart clutter

## Troubleshooting

### Common Issues
- **Too Many Signals**: Increase swing detection length or enable volume filters
- **Missing Signals**: Decrease swing detection length or disable strict confirmations
- **Chart Clutter**: Reduce max boxes/lines or increase transparency
- **Performance Issues**: Reduce historical limits and cleanup frequency

### Performance Optimization
- Set appropriate limits for max boxes and lines
- Use volume filters to reduce false signals
- Enable auto-removal features for FVG and OB
- Consider disabling unused features

## Alert Setup
1. Right-click on the indicator
2. Select "Add Alert"
3. Choose desired alert conditions
4. Configure notification preferences
5. Set alert frequency to "Once Per Bar"

## Version Information
- **Current Version**: 1.0.0
- **Pine Script Version**: v5
- **Compatibility**: All TradingView plans
- **Last Updated**: 2025-06-20

## Support and Updates
This indicator is designed to be self-contained and maintenance-free. All features are optimized for real-time performance and historical accuracy.

For optimal results, combine this indicator with:
- Higher timeframe analysis
- Volume profile studies
- Market sentiment indicators
- Risk management principles

---

**Disclaimer**: This indicator is for educational and analysis purposes only. Always conduct your own research and risk management before making trading decisions.
